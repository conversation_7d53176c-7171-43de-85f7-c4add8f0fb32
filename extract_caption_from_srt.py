import argparse

def extract_caption_text_from_srt(srt_file_path):
    """
    Extracts and returns the full caption text from a .srt subtitle file.
    """
    captions = []
    with open(srt_file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    buffer = []
    for line in lines:
        line = line.strip()
        if not line:
            if buffer:
                captions.append(" ".join(buffer))
                buffer = []
            continue
        if line.isdigit():
            continue  # Skip sequence numbers
        if "-->" in line:
            continue  # Skip timestamps
        buffer.append(line)

    # Append any remaining buffer content
    if buffer:
        captions.append(" ".join(buffer))

    full_text = "\n".join(captions)
    return full_text


# Example usage
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Extract caption text from an SRT file.")
    parser.add_argument("srt_path", help="Path to the .srt file")
    args = parser.parse_args()
    srt_path = args.srt_path
    text = extract_caption_text_from_srt(srt_path)
    with open(srt_path.replace('.srt', '.txt'), 'w', encoding='utf-8') as f:
        f.write(text)
    print(f"Extracted text saved to {srt_path.replace('.srt', '.txt')}")
