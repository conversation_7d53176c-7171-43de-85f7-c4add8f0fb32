#!/usr/bin/env python3
"""
Text to Speech Reader - A Streamlit app that converts text files to HTML and
opens them in Microsoft Edge with read-aloud functionality.
"""
import os
import tempfile
import streamlit as st
import webbrowser
import subprocess
import platform
import time
import logging
from pathlib import Path
import ebooklib
from ebooklib import epub
from bs4 import BeautifulSoup
import base64
import streamlit.components.v1 as components
import re
import shutil # Import shutil for directory cleanup

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# Import the epub converter function
from epub_converter import extract_and_convert_epub


def create_html_from_text(text_content, title="Text Reader", is_markdown=False, dark_mode=True):
    """
    Convert text content to HTML with good readability formatting or render markdown as HTML using marked.js
    
    Args:
        text_content (str): The text content to convert
        title (str): The title of the HTML page
        is_markdown (bool): Whether the text content is markdown
        dark_mode (bool): Whether to use dark mode styling
        
    Returns:
        str: HTML content (with markdown rendered if needed)
    """
    if is_markdown:
        # Minimal HTML that renders markdown using marked.js (GitHub-flavored)
        theme_css = "github-dark" if dark_mode else "github"
        html_content = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/{theme_css}.min.css">
    <style>
        body {{ background: {'#0d1117' if dark_mode else '#f6f8fa'}; color: {'#e0e0e0' if dark_mode else '#24292f'}; margin: 0; }}
        .markdown-body {{
            box-sizing: border-box;
            min-width: 200px;
            max-width: 900px;
            margin: 40px auto;
            padding: 30px;
            background: {'#161b22' if dark_mode else '#fff'};
            border-radius: 8px;
            font-size: 1.2em;
        }}
    </style>
</head>
<body>
    <article class="markdown-body" id="content"></article>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
    <script>
        marked.setOptions({{
            highlight: function(code, lang) {{
                if (lang && hljs.getLanguage(lang)) {{
                    return hljs.highlight(code, {{ language: lang }}).value;
                }}
                return hljs.highlightAuto(code).value;
            }},
            gfm: true
        }});
        const md = `{text_content.replace('`', '\`').replace('${', '\${').replace('\\', '\\\\')}`;
        document.getElementById('content').innerHTML = marked.parse(md);
    </script>
</body>
</html>'''
        return html_content

    # Detect if text contains Chinese characters
    has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text_content)
    
    # Process based on content type
    if not is_markdown:
        # For plain text: preserve line breaks and spacing
        # Escape HTML special characters
        import html
        escaped_text = html.escape(text_content)
        # Convert line breaks to <br> tags and preserve paragraph breaks
        formatted_text = escaped_text.replace('\n\n', '</p><p>').replace('\n', '<br>')
        # Wrap everything in paragraphs
        text_content = f"<p>{formatted_text}</p>"
    # REMOVED: Pre-processing block for markdown was here.
    # Relying on marked.js directly.

    # Add a CSS class for Chinese text if needed
    chinese_class = "chinese" if has_chinese else ""
    
    # Set theme-specific CSS and script URLs
    theme_css = "github-dark" if dark_mode else "github"
    
    html_content = f"""<!DOCTYPE html>
<html lang="{('zh-CN' if has_chinese else 'en')}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <!-- GitHub Markdown CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown.min.css">
    <!-- Highlight.js for syntax highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/{theme_css}.min.css">
    <style>
        /* CSS Reset */
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        /* Base styles */
        html {{
            font-size: 16px;
            scroll-behavior: smooth;
            height: 100%;
        }}
        
        body {{
            min-height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0;
            margin: 0;
            transition: all 0.3s ease;
            overflow-y: scroll;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
            font-size: 22px; /* Larger base font size */
        }}
        
        /* Content container - always centered */
        .content-wrapper {{
            width: 100%;
            max-width: 100%;
            display: flex;
            justify-content: center;
            padding: 0;
            margin: 0;
            flex: 1;
        }}
        
        .container {{
            width: 100%;
            max-width: 800px;
            padding: 20px;
            margin: 20px;
        }}
        
        /* Markdown customization */
        .markdown-body {{
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 45px;
            font-size: 1.6em !important; /* Larger default font size */
        }}
        
        /* CRITICAL: Ensure no inline styling can override our font-size with !important */
        .markdown-body[style*="font-size"] {{
            font-size: 1.6em !important; /* Force our size even with inline styling */
        }}
        
        /* Additional protection against tiny font sizes */
        div[style*="font-size:1px"],
        div[style*="font-size: 1px"],
        .markdown-body[style*="font-size:1px"],
        .markdown-body[style*="font-size: 1px"] {{
            font-size: 1.6em !important;
        }}

        /* Reduce margins for H3 headings for tighter list appearance */
        .markdown-body h3 {{
            margin-top: 1.5em; /* Adjust as needed */
            margin-bottom: 0.5em; /* Adjust as needed */
        }}
        
        /* Chinese text specific styling */
        .chinese .markdown-body p {{
            text-indent: 2em;
            line-height: 2;
            letter-spacing: 0.05em;
        }}
        
        .chinese .markdown-body h3 {{
            margin-top: 30px;
            margin-bottom: 20px;
            font-weight: bold;
        }}
        
        /* Plain text styling - better readability for non-markdown content */
        .markdown-body p {{
            margin-top: 0.8em;
            margin-bottom: 0.8em;
            line-height: 2.0; /* Better line height for readability */
            font-size: 1.6em; /* Larger text size */
            letter-spacing: 0.03em; /* Letter spacing for better readability */
        }}

        .markdown-body br {{
            line-height: 2.0; /* Better line height */
        }}
        
        /* Dark/Light theme overrides */
        body.dark-mode {{
            color: #e0e0e0;
            background-color: #0d1117;
        }}
        
        body.light-mode {{
            color: #24292f;
            background-color: #f6f8fa;
        }}
        
        body.dark-mode .markdown-body {{
            color: #e0e0e0;
            background-color: #161b22;
            border: 1px solid #30363d;
        }}
        
        body.light-mode .markdown-body {{
            color: #24292f;
            background-color: #ffffff;
            border: 1px solid #d0d7de;
        }}
        
        /* Controls */
        .theme-toggle {{
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 100;
            padding: 10px 15px;
            border-radius: 20px;
            border: none;
            cursor: pointer;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }}
        
        .dark-mode .theme-toggle {{
            background-color: #bb86fc;
            color: #121212;
        }}
        
        .light-mode .theme-toggle {{
            background-color: #2c3e50;
            color: white;
        }}
        
        .font-controls {{
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 100;
        }}
        
        .font-button {{
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        
        .dark-mode .font-button {{
            background-color: #333;
            color: #fff;
        }}
        
        .light-mode .font-button {{
            background-color: #eee;
            color: #333;
        }}
        
        /* Reading position indicator */
        .reading-position-indicator {{
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background-color: #bb86fc;
            z-index: 200;
            width: 0%;
            transition: width 0.1s ease;
        }}
        
        /* Responsive design */
        @media (max-width: 768px) {{
            .container {{
                padding: 15px;
                margin: 10px;
            }}
            
            .markdown-body {{
                padding: 25px;
            }}
            
            .theme-toggle {{
                padding: 8px 12px;
                font-size: 14px;
            }}
            
            .font-button {{
                width: 36px;
                height: 36px;
            }}
            
            /* Ensure large text even on mobile */
            body {{
                font-size: 20px;
            }}
            
            .markdown-body {{
                font-size: 1.4em;
            }}
        }}
    </style>
</head>
<body class="{('dark-mode' if dark_mode else 'light-mode') + (' ' + chinese_class if chinese_class else '')}">
    <div class="reading-position-indicator"></div>
    <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">{'☀️ Light Mode' if dark_mode else '🌙 Dark Mode'}</button>
    
    <div class="content-wrapper">
        <div class="container">
            <div class="markdown-body">
                <h1>{title}</h1>
                <div id="content"></div>
            </div>
        </div>
    </div>
    
    <!-- Font size controls -->
    <div class="font-controls">
        <button class="font-button" id="decrease-font" aria-label="Decrease font size">A-</button>
        <button class="font-button" id="increase-font" aria-label="Increase font size">A+</button>
    </div>
    
    <!-- Load Marked.js -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <!-- Load Highlight.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
    
    <script>
        // Store the raw markdown/text content
        const markdownContent = `{text_content.replace('`', '\\`').replace('${', '\\${').replace('\\', '\\\\')}`;
        
        // Configure marked with highlight.js
        marked.setOptions({{
            highlight: function(code, lang) {{
                if (lang && hljs.getLanguage(lang)) {{
                    return hljs.highlight(code, {{ language: lang }}).value;
                }}
                return hljs.highlightAuto(code).value;
            }},
            gfm: true
        }});
        
        // Check if content is pre-formatted HTML (for plain text) or markdown
        if (markdownContent.startsWith('<p>') || markdownContent.startsWith('<div>')) {{
            // For already HTML-formatted plain text, insert directly
            document.getElementById('content').innerHTML = markdownContent;
        }} else {{
            // For markdown, use marked to parse
            document.getElementById('content').innerHTML = marked.parse(markdownContent);
        }}
        
        // Reading position indicator
        function updateReadingPosition() {{
            const totalHeight = document.body.scrollHeight - window.innerHeight;
            const progress = (window.scrollY / totalHeight) * 100;
            document.querySelector('.reading-position-indicator').style.width = progress + '%';
        }}
        
        window.addEventListener('scroll', updateReadingPosition);
        window.addEventListener('resize', updateReadingPosition);
        updateReadingPosition();
    
        // Format Chinese text paragraphs better
        if (document.body.classList.contains('chinese')) {{
            /* Add special formatting for any numbered headings (like "1. Title") */
            document.querySelectorAll('.markdown-body h3').forEach(heading => {{
                const text = heading.textContent;
                if (text && text[0] && !isNaN(parseInt(text[0])) && text.includes('.')) {{
                    heading.style.marginTop = '30px';
                    heading.style.marginBottom = '15px';
                }}
            }});
        }}
    
        // Theme toggle functionality
        const themeToggle = document.getElementById('theme-toggle');
        const body = document.body;
        
        // Check for saved theme preference
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'light') {{
            body.classList.replace('dark-mode', 'light-mode');
            themeToggle.innerHTML = '🌙 Dark Mode';
            themeToggle.setAttribute('aria-label', 'Switch to dark mode');
            // Update highlight.js theme
            document.querySelector('link[href*="highlight"]').href = 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css';
        }}
        
        // Toggle theme on button click
        themeToggle.addEventListener('click', () => {{
            if (body.classList.contains('dark-mode')) {{
                body.classList.replace('dark-mode', 'light-mode');
                themeToggle.innerHTML = '🌙 Dark Mode';
                themeToggle.setAttribute('aria-label', 'Switch to dark mode');
                localStorage.setItem('theme', 'light');
                // Update highlight.js theme
                document.querySelector('link[href*="highlight"]').href = 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css';
            }} else {{
                body.classList.replace('light-mode', 'dark-mode');
                themeToggle.innerHTML = '☀️ Light Mode';
                themeToggle.setAttribute('aria-label', 'Switch to light mode');
                localStorage.setItem('theme', 'dark');
                // Update highlight.js theme
                document.querySelector('link[href*="highlight"]').href = 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css';
            }}
        }});
        
        // Font size adjustment
        const markdownBody = document.querySelector('.markdown-body');
        const increaseFontBtn = document.getElementById('increase-font');
        const decreaseFontBtn = document.getElementById('decrease-font');
        
        // Define default size and safe ranges
        const defaultFontSizePx = 12; // Base size in pixels (matches CSS)
        const minFontSizePx = 10;     // Minimum allowed font size
        const maxFontSizePx = 20;     // Maximum allowed font size
        
        // Function to validate font size is within acceptable range
        function validateFontSize(size) {{
            // Convert to number if it's a string
            const numSize = typeof size === 'string' ? parseInt(size) : size;
            
            // Check if it's a valid number and in acceptable range
            if (isNaN(numSize) || numSize < minFontSizePx || numSize > maxFontSizePx || numSize === 1) {{
                console.warn('Invalid font size detected:', numSize, 'using default instead');
                return defaultFontSizePx;
            }}
            return numSize;
        }}
        
        // Load saved font size preference, with validation
        let currentFontSize = defaultFontSizePx;
        try {{
            const savedFontSize = localStorage.getItem('fontSize');
            if (savedFontSize) {{
                currentFontSize = validateFontSize(parseInt(savedFontSize));
            }}
            // Always save the validated size back to localStorage
            localStorage.setItem('fontSize', currentFontSize);
        }} catch (e) {{
            console.error('Error reading font size from localStorage:', e);
            // In case of any error, use default and don't try to save
            currentFontSize = defaultFontSizePx;
        }}
        
        // IMPORTANT: Apply font size in a defensive way
        // First remove any existing inline styles that might be problematic
        markdownBody.style.removeProperty('font-size');
        // Force a small delay to ensure styles are cleared before setting new ones
        setTimeout(() => {{
            // Set to our validated size in pixels with !important via CSS
            markdownBody.setAttribute('style', 'font-size: ' + currentFontSize + 'px !important');
            
            // Verify the size was applied correctly
            const appliedSize = window.getComputedStyle(markdownBody).fontSize;
            console.log('Applied font size:', appliedSize);
            
            // Final safety check - if size is still tiny, force it again
            if (appliedSize === '1px' || parseInt(appliedSize) < minFontSizePx) {{
                console.warn('Font size still incorrect, forcing through CSS class');
                markdownBody.classList.add('force-readable-size');
                document.head.insertAdjacentHTML('beforeend', 
                    '<style>.force-readable-size {{ font-size: 22px !important; }}</style>');
            }}
        }}, 50);
        
        increaseFontBtn.addEventListener('click', () => {{
            if (currentFontSize < maxFontSizePx) {{
                currentFontSize += 2;
                markdownBody.setAttribute('style', 'font-size: ' + currentFontSize + 'px !important');
                localStorage.setItem('fontSize', currentFontSize);
            }}
        }});
        
        decreaseFontBtn.addEventListener('click', () => {{
            if (currentFontSize > minFontSizePx) {{
                currentFontSize -= 2;
                markdownBody.setAttribute('style', 'font-size: ' + currentFontSize + 'px !important');
                localStorage.setItem('fontSize', currentFontSize);
            }}
        }});
    </script>
</body>
</html>
"""
    return html_content


def open_in_edge(file_path):
    """
    Open the HTML file in Microsoft Edge browser
    
    Args:
        file_path (str): Path to the HTML file
    """
    if platform.system() == "Darwin":  # macOS
        try:
            # Construct the path to Microsoft Edge on macOS
            edge_path = "/Applications/Microsoft Edge.app"
            
            # Use subprocess to open Edge with the file
            subprocess.run(["open", "-a", edge_path, file_path])
            
            # Wait a moment for the browser to load
            time.sleep(2)
            
            # Send the keyboard shortcut to trigger read aloud (Cmd+Shift+U)
            applescript = f'''
            tell application "Microsoft Edge"
                activate
                tell application "System Events"
                    keystroke "u" using {{command down, shift down}}
                end tell
            end tell
            '''
            subprocess.run(["osascript", "-e", applescript])
            
            st.success("HTML file opened in Microsoft Edge with Read Aloud activated!")
        except Exception as e:
            st.error(f"Failed to open in Microsoft Edge: {str(e)}")
            st.info("Make sure Microsoft Edge is installed and try again.")
    else:
        st.error("This feature is currently only supported on macOS.")


def main():
    # Initialize session state for theme if it doesn't exist
    if 'dark_mode' not in st.session_state:
        st.session_state.dark_mode = True  # Default to dark mode
    
    # App title and theme toggle in the same row
    col1, col2 = st.columns([3, 1])
    
    with col1:
        st.title("Text to Speech Reader")
    
    with col2:
        # Toggle button for dark/light mode
        if st.session_state.dark_mode:
            if st.button("☀️ Light Mode"):
                st.session_state.dark_mode = False
                st.experimental_rerun()
        else:
            if st.button("🌙 Dark Mode"):
                st.session_state.dark_mode = True
                st.experimental_rerun()
    
    # Apply custom CSS for Streamlit UI based on theme
    if st.session_state.dark_mode:
        # Dark mode for Streamlit UI
        st.markdown("""
        <style>
        .stApp {
            background-color: #121212;
            color: #e0e0e0;
        }
        </style>
        """, unsafe_allow_html=True)
    
    st.write("Upload a text, markdown, or EPUB file to convert it to HTML and open it in Edge with Read Aloud")
    
    uploaded_file = st.file_uploader("Choose a file", type=["txt", "md", "epub"])
    
    if uploaded_file is not None:
        # Process based on file type
        file_extension = Path(uploaded_file.name).suffix.lower()
        title = Path(uploaded_file.name).stem # Default title
        
        # Option to customize the page title (moved up for EPUB)
        custom_title = st.text_input("HTML Page Title", value=title)

        if file_extension == '.epub':
            file_type = "EPUB"
            st.subheader("Processing EPUB...")
            # Use epub_converter logic
            if st.button(f"Convert {file_type} and Open in Edge"):
                # Create a temporary directory for conversion artifacts
                with tempfile.TemporaryDirectory() as temp_dir:
                    # Save uploaded EPUB to a temporary file within the temp dir
                    temp_epub_path = os.path.join(temp_dir, uploaded_file.name)
                    with open(temp_epub_path, "wb") as f:
                        f.write(uploaded_file.getvalue())

                    # Run the epub converter
                    st.info("Converting EPUB to HTML, this may take a moment...")
                    # Pass the temp dir as the output directory
                    generated_html_path = extract_and_convert_epub(temp_epub_path, temp_dir)

                    if generated_html_path and os.path.exists(generated_html_path):
                        st.success("EPUB converted successfully!")
                        
                        # Ensure the CSS file exists in the temporary directory
                        css_path = os.path.join(temp_dir, "reading_style.css")
                        if not os.path.exists(css_path):
                            # Create a CSS file directly in the temporary directory if it doesn't exist
                            from epub_converter import create_perfect_reading_style_css
                            create_perfect_reading_style_css(temp_dir)
                            st.info("Created missing CSS file")
                            
                        # Inspect the directory to help with debugging
                        temp_dir_contents = os.listdir(temp_dir)
                        logging.info(f"Temporary directory contents: {temp_dir_contents}")
                        
                        # Modify the HTML file to use inline CSS if CSS file is still missing
                        if not os.path.exists(css_path):
                            logging.warning("CSS file still missing, embedding CSS inline")
                            try:
                                with open(generated_html_path, 'r', encoding='utf-8') as file:
                                    html_content = file.read()
                                    
                                # Modify the HTML to inline the CSS reference and add basic styles
                                html_content = html_content.replace('<link rel="stylesheet" href="reading_style.css">', 
                                                                  """<style>
                                                                  /* Basic styles inline */
                                                                  body { font-family: sans-serif; font-size: 0.8em; line-height: 1.6; }
                                                                  .content-wrapper { max-width: 800px; margin: 0 auto; padding: 20px; }
                                                                  /* Font controls */
                                                                  .font-controls { position: fixed; bottom: 20px; right: 20px; display: flex; gap: 10px; z-index: 100; }
                                                                  .font-button { width: 40px; height: 40px; border-radius: 50%; cursor: pointer; }
                                                                  </style>""")
                                
                                # Write the modified content back to the file
                                with open(generated_html_path, 'w', encoding='utf-8') as file:
                                    file.write(html_content)
                                logging.info("Added inline CSS as fallback")
                            except Exception as e:
                                logging.error(f"Error modifying HTML for inline CSS: {e}")
                        
                        # Open the generated HTML file in Edge
                        open_in_edge(generated_html_path)
                        
                        # Provide a download link for the generated HTML file
                        with open(generated_html_path, "rb") as file:
                            st.download_button(
                                label="Download Converted HTML File",
                                data=file,
                                file_name=f"{custom_title}.html",
                                mime="text/html"
                            )
                    else:
                        st.error("Failed to convert EPUB file.")
                # temp_dir and its contents are automatically removed here

        else: # Handle TXT and MD files
            text_content = uploaded_file.getvalue().decode("utf-8")
            is_markdown = file_extension == '.md'
            
            # Display a preview of the text content
            st.subheader("Content Preview")
            if is_markdown:
                st.markdown(text_content)
            else:
                st.text_area("Content", text_content, height=300)

            if is_markdown:
                file_type = "Markdown"
            else:
                file_type = "Text"

            # Create HTML button for TXT/MD
            if st.button(f"Convert {file_type} and Open in Edge"):
                # Generate HTML content using the original function
                html_content = create_html_from_text(
                    text_content,
                    custom_title,
                    is_markdown=is_markdown,
                    dark_mode=st.session_state.dark_mode
                )

                # Create a temporary HTML file
                # Use a context manager for the temporary file
                with tempfile.NamedTemporaryFile(delete=False, suffix='.html', mode='w', encoding='utf-8') as tmp_file:
                    tmp_file.write(html_content)
                    html_path = tmp_file.name
                
                try:
                    # Open the HTML file in Edge browser
                    open_in_edge(html_path)

                    # Provide a download link for the HTML file
                    with open(html_path, "rb") as file:
                        st.download_button(
                            label="Download HTML File",
                            data=file,
                            file_name=f"{custom_title}.html",
                            mime="text/html"
                        )
                finally:
                     # Clean up the temporary HTML file after use
                     if os.path.exists(html_path):
                         os.unlink(html_path)


if __name__ == "__main__":
    main()