import torch
from transformers import AutoModelForSeq2SeqLM, AutoTokenizer

def translate(text, source_lang, target_lang, model, tokenizer, max_length=100):
    """
    Translates text from source language to target language using NLLB model.
    
    Args:
        text (str): Text to translate
        source_lang (str): Source language code (e.g., 'eng_Latn')
        target_lang (str): Target language code (e.g., 'fra_Latn')
        model: The loaded translation model
        tokenizer: The loaded tokenizer
        max_length (int): Maximum length of generated translation
        
    Returns:
        str: Translated text
    """
    # Add source language as prefix
    src_text = f"{source_lang}: {text}"
    inputs = tokenizer(src_text, return_tensors="pt")
    
    # Forward through the model with the correct target language token
    target_language_id = tokenizer.convert_tokens_to_ids(target_lang)
    translated_tokens = model.generate(
        **inputs,
        forced_bos_token_id=target_language_id,
        max_length=max_length
    )
    
    # Decode the generated translation
    translation = tokenizer.batch_decode(translated_tokens, skip_special_tokens=True)[0]
    return translation

def main():
    print("Loading NLLB-200 distilled 1.3B model...")
    model_name = "facebook/nllb-200-distilled-1.3B"
    
    # Load model and tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForSeq2SeqLM.from_pretrained(model_name)
    
    print("Model loaded successfully!")
    
    # Sample texts to translate
    texts = [
        "Hello, how are you doing today?",
        "Machine translation is an exciting field of artificial intelligence.",
        "I love learning new languages."
    ]
    
    # Some language pairs to test
    language_pairs = [
        ("eng_Latn", "fra_Latn"),  # English to French
        ("eng_Latn", "spa_Latn"),  # English to Spanish
        ("eng_Latn", "deu_Latn"),  # English to German
        ("eng_Latn", "zho_Hans"),  # English to Chinese (Simplified)
        ("eng_Latn", "rus_Cyrl"),  # English to Russian
    ]
    
    # Display some available languages
    print("\nSome available language codes in NLLB-200:")
    selected_langs = ["eng_Latn", "fra_Latn", "spa_Latn", "deu_Latn", 
                      "zho_Hans", "rus_Cyrl", "ara_Arab", "hin_Deva", "jpn_Jpan"]
    for code in selected_langs:
        print(f"  - {code}")
    
    print("\nRunning translation examples...")
    
    # Test translation for each text and language pair
    for text in texts:
        print(f"\nOriginal text: {text}")
        
        for src_lang, tgt_lang in language_pairs:
            translation = translate(text, src_lang, tgt_lang, model, tokenizer)
            print(f"{src_lang} → {tgt_lang}: {translation}")
    
    # Interactive mode
    print("\n" + "="*50)
    print("INTERACTIVE MODE: Enter text to translate or 'q' to quit")
    print("="*50)
    
    while True:
        text = input("\nEnter text to translate (or 'q' to quit): ")
        if text.lower() == 'q':
            break
            
        src_lang = input("Source language code (default: eng_Latn): ").strip() or "eng_Latn"
        tgt_lang = input("Target language code (default: zho_Hans): ").strip() or "zho_Hans"
        
        try:
            translation = translate(text, src_lang, tgt_lang, model, tokenizer)
            print(f"\nTranslation ({src_lang} → {tgt_lang}):")
            print(translation)
        except Exception as e:
            print(f"Translation error: {e}")

if __name__ == "__main__":
    main()
