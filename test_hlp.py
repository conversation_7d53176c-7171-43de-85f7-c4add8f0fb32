import hanlp

# For HanLP 2.1+, the `sent` task directly gives sentences:
# This will download a model the first time you run it.
hanlp_sent_model = hanlp.load(hanlp.pretrained.tok.COARSE_ELECTRA_SMALL_ZH)

text_hanlp_test = "他说：“我很好。”然后他微笑着离开了。她大喊：“救命啊！”旁边的人都惊呆了。"
sentences_hanlp = hanlp_sent_model(text_hanlp_test)

print("\n--- HanLP Example (Quote handling) ---")
for i, sentence in enumerate(sentences_hanlp):
    print(f"{i+1}: {sentence.strip()}")