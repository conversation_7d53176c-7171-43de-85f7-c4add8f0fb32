#!/usr/bin/env python
# -*- coding: utf-8 -*-

import ebooklib
from ebooklib import epub
from bs4 import BeautifulSoup, NavigableString, Tag # Import Tag
import os
import base64
import codecs # For writing files with correct encoding
import uuid # For unique image names if needed
import re
import logging # Added logging for better feedback
import shutil # Import shutil for directory cleanup

# --- Logging Setup ---
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# --- Functions ---

def create_perfect_reading_style_css(output_dir):
    """
    Creates a CSS file (reading_style.css) with styles optimized for readability,
    using sans-serif fonts and adjusted paragraph spacing, suitable for CJK.

    Args:
        output_dir (str): The directory where the CSS file will be saved.

    Returns:
        str: The filename of the created CSS file, or None if creation failed.
    """
    css_filename = "reading_style.css"
    css_path = os.path.join(output_dir, css_filename)
    # CSS content focused on readability, with CJK adjustments
    css_content = """
/* --- Reading Style CSS (CJK Optimized - Sans Serif) --- */
body {
    /* Changed to prioritize Sans Serif CJK fonts */
    font-family: Roboto, "Noto Sans", Helvetica, Arial, "Segoe UI", "Source Han Sans SC VF", "Source Han SC", "Noto Sans SC", STXihei, 华文细黑, "Microsoft YaHei", 微软雅黑, sans-serif;
    line-height: 1.7; /* Adjusted line height for sans-serif */
    white-space: normal; /* Normal white space handling */
    background-color: #f0f2f5; /* Slightly off-white background for body */
    color: #333;
    font-size: 1.05em; /* Base font size, can be adjusted by JS */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color 0.3s ease, color 0.3s ease; /* Add transition */
    min-height: 100vh; /* Ensure body takes full height */
    margin: 0; /* Remove default body margin */
    padding: 0; /* Remove default body padding */
}

/* --- New Content Wrapper --- */
.content-wrapper {
    width: 100%; /* Take full width available */
    max-width: 800px; /* Limit content width */
    margin: 3em auto; /* Center the block vertically (top/bottom) and horizontally */
    padding: 1.5em 2.5em; /* Adjusted padding for visual separation */
    box-sizing: border-box; /* Include padding in width calculation */
    background-color: #fefefe; /* White background for content */
    border-radius: 8px; /* Optional: Add rounded corners */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); /* Optional: Add subtle shadow */
    transition: background-color 0.3s ease; /* Add transition for background */
}

/* --- Dark Mode Styles --- */
body.dark-mode {
    background-color: #0d1117; /* Dark background for body */
    color: #e0e0e0; /* Light text */
}

body.dark-mode .content-wrapper {
    background-color: #161b22; /* Slightly lighter dark background for content */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); /* Adjusted shadow for dark mode */
}

/* --- Headings --- */
h1, h2, h3, h4, h5, h6 {
     /* Ensure headings use compatible sans-serif fonts */
    font-family: inherit; /* Inherit from body for consistency */
    line-height: 1.4;
    margin-top: 1.8em;
    margin-bottom: 0.8em;
    color: #1a1a1a;
    font-weight: 600; /* Keep headings bold */
}

h1 {
    font-size: 2.2em; /* Adjusted size */
    border-bottom: 2px solid #eee;
    padding-bottom: 0.4em;
    text-align: center;
}

h2 {
    font-size: 1.7em; /* Adjusted size */
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 0.3em;
}

h3 {
    font-size: 1.4em; /* Adjusted size */
}

h4 {
    font-size: 1.2em; /* Adjusted size */
    font-weight: bold;
}

/* --- Dark Mode Headings --- */
body.dark-mode h1,
body.dark-mode h2,
body.dark-mode h3,
body.dark-mode h4,
body.dark-mode h5,
body.dark-mode h6 {
    color: #f5f5f5; /* Lighter heading color */
    border-color: #444; /* Darker border */
}

/* --- Paragraphs and Links --- */
p {
    margin-top: 0;
    margin-bottom: 0em; /* Reduced space between paragraphs */
    text-align: left;
    text-indent: 2em; /* Keep first line indent */
    hyphens: auto;
    widows: 2;
    orphans: 2;
}

# /* Remove indent for paragraphs immediately following a heading */
# h1 + p, h2 + p, h3 + p, h4 + p, h5 + p, h6 + p {
#     text-indent: 0;
# }

a {
    color: #0066cc;
    text-decoration: none;
    transition: color 0.2s ease-in-out;
}

a:hover, a:focus {
    color: #004080;
    text-decoration: underline;
}

/* --- Dark Mode Links --- */
body.dark-mode a {
    color: #66b3ff; /* Lighter blue for links */
}

body.dark-mode a:hover,
body.dark-mode a:focus {
    color: #99ccff;
}

/* --- Images --- */
img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 2em auto;
    border: 1px solid #ddd;
    padding: 6px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    box-sizing: border-box;
}

/* --- Dark Mode Images --- */
body.dark-mode img {
    border-color: #555; /* Darker border */
    background-color: #2a2a2a; /* Slightly lighter dark background for image padding */
    box-shadow: 0 2px 5px rgba(0,0,0,0.4); /* Adjusted shadow */
}

/* --- Other Elements --- */
blockquote {
    border-left: 5px solid #ccc;
    padding-left: 1.5em;
    margin-left: 0;
    margin-right: 0;
    margin-top: 1.5em;
    margin-bottom: 1.5em;
    /* font-style: italic; */ /* Removed italic for sans-serif body */
    color: #555;
    text-indent: 0;
}

/* --- Dark Mode Blockquotes --- */
body.dark-mode blockquote {
    border-left-color: #666;
    color: #bbb;
}

code, pre {
    font-family: Roboto, "Noto Sans", Helvetica, Arial, "Segoe UI", "Source Han Sans SC VF", "Source Han SC", "Noto Sans SC", STXihei, 华文细黑, "Microsoft YaHei", 微软雅黑, sans-serif;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    padding: 0.1em 0.4em;
    border-radius: 4px;
    font-size: 0.9em;
    text-indent: 0;
}

/* --- Dark Mode Code Blocks --- */
body.dark-mode code,
body.dark_mode pre {
    background-color: #282c34; /* Common dark code background */
    border-color: #444;
    color: #abb2bf; /* Common light code text color */
}

pre {
    padding: 1em;
    overflow-x: auto;
    # white-space: pre-wrap;
    word-wrap: break-word;
}

hr {
    border: 0;
    height: 1px;
    background-color: #eee;
    margin: 3em 0;
}

/* --- Dark Mode Horizontal Rule --- */
body.dark-mode hr {
    background-color: #444;
}

/* --- Table of Contents Styling --- */
.toc {
    margin-bottom: 3em;
    padding: 1.5em;
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 5px;
}

/* --- Dark Mode Table of Contents --- */
body.dark-mode .toc {
    background-color: #252525;
    border-color: #444;
}

body.dark-mode .toc h2 {
    color: #f5f5f5;
    border-bottom: none;
}

.toc h2 {
    margin-top: 0;
    margin-bottom: 1em;
    font-size: 1.5em;
    text-align: center;
    border-bottom: none;
}

.toc ul {
    list-style-type: none;
    padding-left: 0;
    margin-bottom: 0;
}

.toc ul ul {
    padding-left: 1.5em;
    margin-top: 0.3em;
}


.toc li {
    margin-bottom: 0.6em;
    text-indent: 0;
}

.toc a {
    text-decoration: none;
}

.toc a:hover {
    text-decoration: underline;
}

/* --- Main Content Wrapper --- */
.main-content {
    /* Inherits body styles */
}

/* --- Chapter/Section Wrapper --- */
.epub-chapter {
    padding-top: 1em;
}

/* --- Theme Toggle Button --- */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    padding: 8px 15px;
    border-radius: 20px;
    border: 1px solid transparent;
    cursor: pointer;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Light mode button style */
body:not(.dark-mode) .theme-toggle {
    background-color: #eee;
    color: #333;
    border-color: #ccc;
}

/* Dark mode button style */
body.dark-mode .theme-toggle {
    background-color: #333;
    color: #eee;
    border-color: #555;
}

/* --- Font Size Controls --- */
.font-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    z-index: 1000;
}

.font-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 1px solid transparent;
    cursor: pointer;
    font-weight: bold;
    font-size: 16px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Light mode button style */
body:not(.dark-mode) .font-button {
    background-color: #eee;
    color: #333;
    border-color: #ccc;
}

/* Dark mode button style */
body.dark-mode .font-button {
    background-color: #333;
    color: #eee;
    border-color: #555;
}

/* --- Media Queries for Responsiveness --- */
@media (max-width: 600px) {
    body {
        font-size: 1em; /* Base size for mobile */
        line-height: 1.6; /* Adjust line height */
    }
    .content-wrapper {
        margin: 1.5em auto; /* Adjust wrapper margin for mobile */
        padding: 1em 1.5em; /* Adjust wrapper padding for mobile */
        border-radius: 0; /* Optional: Remove radius on mobile */
        box-shadow: none; /* Optional: Remove shadow on mobile */
    }
    h1 { font-size: 1.8em; } /* Adjust heading sizes for mobile */
    h2 { font-size: 1.5em; }
    h3 { font-size: 1.3em; }
    h4 { font-size: 1.1em; }
    p {
        text-align: left;
        text-indent: 2em;
        margin-bottom: 0.7em; /* Adjust mobile paragraph spacing */
     }
    h1 + p, h2 + p, h3 + p, h4 + p, h5 + p, h6 + p {
        text-indent: 0;
    }
    .font-controls {
        bottom: 15px;
        right: 15px;
    }
    .font-button {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }
}
    """
    try:
        # Ensure the output directory exists before writing
        os.makedirs(output_dir, exist_ok=True)
        with codecs.open(css_path, "w", "utf-8") as f:
            f.write(css_content)
        logging.info(f"Created CSS file: {css_path}")
        return css_filename
    except IOError as e:
        logging.error(f"Error creating CSS file {css_path}: {e}")
        return None
    except Exception as e:
        logging.error(f"Unexpected error creating CSS file: {e}")
        return None


def sanitize_id(input_string):
    """
    Sanitizes a string to be used as an HTML ID.
    Removes invalid characters and ensures it starts with a letter.
    """
    # Remove leading non-alphabetic characters
    sanitized = re.sub(r'^[^a-zA-Z]+', '', input_string)
    # Replace invalid characters (not alphanumeric, hyphen, or underscore) with hyphen
    sanitized = re.sub(r'[^\w\-]+', '-', sanitized)
    # If empty after sanitization (e.g., input was only symbols), generate a default ID
    if not sanitized:
        return f"epub-item-{uuid.uuid4().hex[:8]}"
    return sanitized


def extract_and_convert_epub(epub_path, output_dir):
    """
    Extracts content from an EPUB file and converts it to a single, styled HTML file.

    Args:
        epub_path (str): Path to the input EPUB file.
        output_dir (str): Directory to save the output HTML and assets.

    Returns:
        str: The absolute path to the generated HTML file, or None if conversion failed.
    """
    if not os.path.exists(epub_path):
        logging.error(f"EPUB file not found at {epub_path}")
        return None

    if not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            logging.info(f"Created output directory: {output_dir}")
        except OSError as e:
            logging.error(f"Error creating output directory {output_dir}: {e}")
            return None

    img_dir = os.path.join(output_dir, "images")
    if not os.path.exists(img_dir):
        try:
            os.makedirs(img_dir)
        except OSError as e:
            logging.error(f"Error creating images directory {img_dir}: {e}")
            return None

    # Create the CSS content (but we'll embed it directly in HTML)
    css_content = """
/* --- Reading Style CSS (CJK Optimized - Sans Serif) --- */
body {
    /* Changed to prioritize Sans Serif CJK fonts */
    font-family: Roboto, "Noto Sans", Helvetica, Arial, "Segoe UI", "Source Han Sans SC VF", "Source Han SC", "Noto Sans SC", STXihei, 华文细黑, "Microsoft YaHei", 微软雅黑, sans-serif;
    line-height: 1.7; /* Adjusted line height for sans-serif */
    white-space: normal; /* Normal white space handling */
    background-color: #f0f2f5; /* Slightly off-white background for body */
    color: #333;
    font-size: 0.8em !important; /* Smaller base font size */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color 0.3s ease, color 0.3s ease; /* Add transition */
    min-height: 100vh; /* Ensure body takes full height */
    margin: 0; /* Remove default body margin */
    padding: 0; /* Remove default body padding */
}

/* --- New Content Wrapper --- */
.content-wrapper {
    width: 100%; /* Take full width available */
    max-width: 800px; /* Limit content width */
    margin: 3em auto; /* Center the block vertically (top/bottom) and horizontally */
    padding: 1.5em 2.5em; /* Adjusted padding for visual separation */
    box-sizing: border-box; /* Include padding in width calculation */
    background-color: #fefefe; /* White background for content */
    border-radius: 8px; /* Optional: Add rounded corners */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); /* Optional: Add subtle shadow */
    transition: background-color 0.3s ease; /* Add transition for background */
}

/* --- Dark Mode Styles --- */
body.dark-mode {
    background-color: #0d1117; /* Dark background for body */
    color: #e0e0e0; /* Light text */
}

body.dark-mode .content-wrapper {
    background-color: #161b22; /* Slightly lighter dark background for content */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); /* Adjusted shadow for dark mode */
}

/* --- Headings --- */
h1, h2, h3, h4, h5, h6 {
     /* Ensure headings use compatible sans-serif fonts */
    font-family: inherit; /* Inherit from body for consistency */
    line-height: 1.4;
    margin-top: 1.8em;
    margin-bottom: 0.8em;
    color: #1a1a1a;
    font-weight: 600; /* Keep headings bold */
}

h1 {
    font-size: 2.2em; /* Adjusted size */
    border-bottom: 2px solid #eee;
    padding-bottom: 0.4em;
    text-align: center;
}

h2 {
    font-size: 1.7em; /* Adjusted size */
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 0.3em;
}

h3 {
    font-size: 1.4em; /* Adjusted size */
}

h4 {
    font-size: 1.2em; /* Adjusted size */
    font-weight: bold;
}

/* --- Dark Mode Headings --- */
body.dark-mode h1,
body.dark-mode h2,
body.dark-mode h3,
body.dark-mode h4,
body.dark-mode h5,
body.dark-mode h6 {
    color: #f5f5f5; /* Lighter heading color */
    border-color: #444; /* Darker border */
}

/* --- Paragraphs and Links --- */
p {
    margin-top: 0;
    margin-bottom: 0em; /* Reduced space between paragraphs */
    text-align: left;
    text-indent: 2em; /* Keep first line indent */
    hyphens: auto;
    widows: 2;
    orphans: 2;
}

# /* Remove indent for paragraphs immediately following a heading */
# h1 + p, h2 + p, h3 + p, h4 + p, h5 + p, h6 + p {
#     text-indent: 0;
# }

a {
    color: #0066cc;
    text-decoration: none;
    transition: color 0.2s ease-in-out;
}

a:hover, a:focus {
    color: #004080;
    text-decoration: underline;
}

/* --- Dark Mode Links --- */
body.dark-mode a {
    color: #66b3ff; /* Lighter blue for links */
}

body.dark-mode a:hover,
body.dark-mode a:focus {
    color: #99ccff;
}

/* --- Images --- */
img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 2em auto;
    border: 1px solid #ddd;
    padding: 6px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    box-sizing: border-box;
}

/* --- Dark Mode Images --- */
body.dark-mode img {
    border-color: #555; /* Darker border */
    background-color: #2a2a2a; /* Slightly lighter dark background for image padding */
    box-shadow: 0 2px 5px rgba(0,0,0,0.4); /* Adjusted shadow */
}

/* --- Other Elements --- */
blockquote {
    border-left: 5px solid #ccc;
    padding-left: 1.5em;
    margin-left: 0;
    margin-right: 0;
    margin-top: 1.5em;
    margin-bottom: 1.5em;
    /* font-style: italic; */ /* Removed italic for sans-serif body */
    color: #555;
    text-indent: 0;
}

/* --- Dark Mode Blockquotes --- */
body.dark-mode blockquote {
    border-left-color: #666;
    color: #bbb;
}

code, pre {
    font-family: Roboto, "Noto Sans", Helvetica, Arial, "Segoe UI", "Source Han Sans SC VF", "Source Han SC", "Noto Sans SC", STXihei, 华文细黑, "Microsoft YaHei", 微软雅黑, sans-serif;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    padding: 0.1em 0.4em;
    border-radius: 4px;
    font-size: 0.9em;
    text-indent: 0;
}

/* --- Dark Mode Code Blocks --- */
body.dark-mode code,
body.dark_mode pre {
    background-color: #282c34; /* Common dark code background */
    border-color: #444;
    color: #abb2bf; /* Common light code text color */
}

pre {
    padding: 1em;
    overflow-x: auto;
    # white-space: pre-wrap;
    word-wrap: break-word;
}

hr {
    border: 0;
    height: 1px;
    background-color: #eee;
    margin: 3em 0;
}

/* --- Dark Mode Horizontal Rule --- */
body.dark-mode hr {
    background-color: #444;
}

/* --- Table of Contents Styling --- */
.toc {
    margin-bottom: 3em;
    padding: 1.5em;
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 5px;
}

/* --- Dark Mode Table of Contents --- */
body.dark-mode .toc {
    background-color: #252525;
    border-color: #444;
}

body.dark-mode .toc h2 {
    color: #f5f5f5;
    border-bottom: none;
}

.toc h2 {
    margin-top: 0;
    margin-bottom: 1em;
    font-size: 1.5em;
    text-align: center;
    border-bottom: none;
}

.toc ul {
    list-style-type: none;
    padding-left: 0;
    margin-bottom: 0;
}

.toc ul ul {
    padding-left: 1.5em;
    margin-top: 0.3em;
}


.toc li {
    margin-bottom: 0.6em;
    text-indent: 0;
}

.toc a {
    text-decoration: none;
}

.toc a:hover {
    text-decoration: underline;
}

/* --- Main Content Wrapper --- */
.main-content {
    /* Inherits body styles */
}

/* --- Chapter/Section Wrapper --- */
.epub-chapter {
    padding-top: 1em;
}

/* --- Theme Toggle Button --- */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    padding: 8px 15px;
    border-radius: 20px;
    border: 1px solid transparent;
    cursor: pointer;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Light mode button style */
body:not(.dark-mode) .theme-toggle {
    background-color: #eee;
    color: #333;
    border-color: #ccc;
}

/* Dark mode button style */
body.dark-mode .theme-toggle {
    background-color: #333;
    color: #eee;
    border-color: #555;
}

/* --- Font Size Controls --- */
.font-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    z-index: 1000;
}

.font-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 1px solid transparent;
    cursor: pointer;
    font-weight: bold;
    font-size: 16px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Light mode button style */
body:not(.dark-mode) .font-button {
    background-color: #eee;
    color: #333;
    border-color: #ccc;
}

/* Dark mode button style */
body.dark-mode .font-button {
    background-color: #333;
    color: #eee;
    border-color: #555;
}

/* --- Media Queries for Responsiveness --- */
@media (max-width: 600px) {
    body {
        font-size: 0.8em; /* Base size for mobile */
        line-height: 1.6; /* Adjust line height */
    }
    .content-wrapper {
        margin: 1.5em auto; /* Adjust wrapper margin for mobile */
        padding: 1em 1.5em; /* Adjust wrapper padding for mobile */
        border-radius: 0; /* Optional: Remove radius on mobile */
        box-shadow: none; /* Optional: Remove shadow on mobile */
    }
    h1 { font-size: 1.8em; } /* Adjust heading sizes for mobile */
    h2 { font-size: 1.5em; }
    h3 { font-size: 1.3em; }
    h4 { font-size: 1.1em; }
    p {
        text-align: left;
        text-indent: 2em;
        margin-bottom: 0.7em; /* Adjust mobile paragraph spacing */
     }
    h1 + p, h2 + p, h3 + p, h4 + p, h5 + p, h6 + p {
        text-indent: 0;
    }
    .font-controls {
        bottom: 15px;
        right: 15px;
    }
    .font-button {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }
}
    """

    try:
        logging.info(f"Reading EPUB file: {epub_path}")
        book = epub.read_epub(epub_path)
    except Exception as e:
        logging.error(f"Error reading EPUB file {epub_path}: {e}")
        return None

    # --- Metadata ---
    book_title = "Untitled Book"
    book_language = "en" # Default language
    try:
        metadata_title = book.get_metadata('DC', 'title')
        if metadata_title:
            book_title = metadata_title[0][0]

        metadata_lang = book.get_metadata('DC', 'language')
        if metadata_lang:
            # Use the first language code found
            lang_code = metadata_lang[0][0]
            # Basic normalization (e.g., zh-CN -> zh)
            book_language = lang_code.split('-')[0]

    except Exception as e:
        logging.warning(f"Could not extract some metadata: {e}")

    logging.info(f"Processing '{book_title}' (Language: {book_language})...")

    # --- Process Content (Chapters/Documents) ---
    html_content_full = "" # Stores the final HTML body content
    items_dict = {item.id: item for item in book.get_items()}
    processed_item_ids = set() # Keep track of items added to avoid duplicates
    image_map = {} # Maps original image src (within EPUB) to new path (relative to HTML)
    item_id_map = {} # Maps item href to generated chapter div ID

    # Determine reading order: Prioritize spine, then add remaining HTML docs
    content_items_ordered = []
    try:
        spine = book.spine
        if spine:
            logging.info("Using EPUB spine for content order.")
            for item_id, _ in spine:
                if item_id in items_dict:
                    item = items_dict[item_id]
                    # Only process HTML/XHTML documents from the spine
                    if isinstance(item, epub.EpubHtml):
                         content_items_ordered.append(item)
                         processed_item_ids.add(item.id)
                    else:
                         logging.warning(f"Spine item '{item_id}' is not HTML, skipping.")
                else:
                    logging.warning(f"Spine item ID '{item_id}' not found in book items.")
        else:
            logging.warning("EPUB spine is empty or invalid. Processing all HTML items in default order.")
            # Fallback if spine is empty
            content_items_ordered = list(book.get_items_of_type(ebooklib.ITEM_DOCUMENT))
            processed_item_ids.update(item.id for item in content_items_ordered)

    except Exception as e:
        logging.warning(f"Could not reliably use spine for order ({e}). Processing all HTML items.")
        # Fallback if spine processing fails
        content_items_ordered = list(book.get_items_of_type(ebooklib.ITEM_DOCUMENT))
        processed_item_ids.update(item.id for item in content_items_ordered)

    # Add any HTML documents not included in the spine (e.g., title page, copyright)
    # These will be appended after the spine content
    additional_items = []
    for item in book.get_items_of_type(ebooklib.ITEM_DOCUMENT):
        if item.id not in processed_item_ids:
            additional_items.append(item)
            logging.info(f"Adding non-spine HTML item: {item.get_name()}")

    content_items_ordered.extend(additional_items)

    logging.info(f"Processing {len(content_items_ordered)} HTML content items...")

    # Process each item
    for item_index, item in enumerate(content_items_ordered):
        logging.info(f"  Processing item {item_index + 1}/{len(content_items_ordered)}: {item.get_name()} (ID: {item.id})")
        try:
            # Decode content safely
            raw_content = item.get_content()
            try:
                content_html = raw_content.decode('utf-8', errors='replace')
            except UnicodeDecodeError:
                 logging.warning(f"    UTF-8 decode failed for {item.get_name()}, trying default encoding with replacement.")
                 content_html = raw_content.decode(errors='replace') # Fallback decode

            soup = BeautifulSoup(content_html, 'lxml') # Use lxml parser

            # Extract only the body content, or the whole HTML if no body tag
            body_content = soup.find('body')
            if not body_content:
                # If no body, try to find content within html tag, or use the whole soup
                body_content = soup.find('html') or soup
                if body_content is soup:
                     logging.warning(f"    No <body> or <html> tag found in {item.get_name()}, processing entire item content.")


            # --- Pre-process body_content: Remove unnecessary line breaks within text nodes ---
            # This can help if the EPUB source has excessive <br> or newlines
            # Be cautious as this might merge intended short lines.
            # for text_node in body_content.find_all(string=True):
            #     if isinstance(text_node, NavigableString) and text_node.parent.name not in ['pre', 'code']:
            #          cleaned_text = re.sub(r'\s+', ' ', text_node.strip()).strip()
            #          if cleaned_text:
            #              text_node.replace_with(cleaned_text + ' ') # Add space for separation
            #          else:
            #              text_node.extract() # Remove empty text nodes

            # Generate a unique and valid ID for this chapter/section wrapper
            # Use item.get_name() as it's usually more descriptive and stable than item.id
            chapter_id = sanitize_id(f"chapter-{item.get_name()}")
            item_id_map[item.get_name()] = chapter_id # Map item's internal path to this ID

            # --- Process Images within this item ---
            for img_tag in body_content.find_all(['img', 'image']): # Include SVG 'image' tag
                img_src = None
                if img_tag.name == 'img':
                    img_src = img_tag.get('src')
                elif img_tag.name == 'image':
                     img_src = img_tag.get('xlink:href') # SVG uses xlink:href

                if not img_src:
                    logging.warning(f"    Skipping image tag with no src/href in {item.get_name()}")
                    continue

                # Resolve image path relative to the current HTML item's location
                try:
                    base_path = os.path.dirname(item.get_name()) # e.g., OEBPS/Text
                    # Correctly join and normalize the path
                    abs_img_path_epub = os.path.normpath(os.path.join(base_path, img_src))
                    # EPUB paths use forward slashes
                    abs_img_path_epub = abs_img_path_epub.replace('\\', '/')
                except Exception as e:
                    logging.warning(f"    Could not resolve image path '{img_src}' in {item.get_name()}: {e}")
                    img_tag.decompose() # Remove tag if path resolution fails
                    continue

                # Check if we've already processed and saved this image
                if abs_img_path_epub in image_map:
                    # Update src/href attribute correctly based on tag type
                    if img_tag.name == 'img':
                         img_tag['src'] = image_map[abs_img_path_epub]
                    elif img_tag.name == 'image':
                         img_tag['xlink:href'] = image_map[abs_img_path_epub]
                    continue # Skip saving again

                # Find the image item in the EPUB manifest
                img_item = book.get_item_with_href(abs_img_path_epub)

                if img_item:
                    # Generate a safe filename for the image
                    original_filename = os.path.basename(img_item.get_name())
                    # Clean the filename (allow letters, numbers, underscore, hyphen, dot)
                    safe_filename_base = re.sub(r'[^\w.\-]+', '_', original_filename)
                    # Ensure uniqueness if needed
                    safe_filename = f"{uuid.uuid4().hex[:8]}_{safe_filename_base}"

                    img_output_path_rel = os.path.join("images", safe_filename).replace('\\', '/')
                    img_output_path_abs = os.path.join(output_dir, img_output_path_rel)

                    # Save image content
                    try:
                        with open(img_output_path_abs, 'wb') as img_file:
                            img_file.write(img_item.get_content())
                        image_map[abs_img_path_epub] = img_output_path_rel # Store mapping
                        # Update src/href attribute correctly
                        if img_tag.name == 'img':
                             img_tag['src'] = img_output_path_rel
                        elif img_tag.name == 'image':
                             img_tag['xlink:href'] = img_output_path_rel
                        logging.info(f"    Extracted image: {original_filename} -> {img_output_path_rel}")
                    except IOError as e:
                        logging.error(f"    Error saving image {img_output_path_abs}: {e}")
                        img_tag.decompose() # Remove broken image tag
                    except Exception as e:
                        logging.error(f"    Unexpected error processing image {img_item.get_name()}: {e}")
                        img_tag.decompose()
                else:
                    logging.warning(f"    Image not found in EPUB manifest: '{abs_img_path_epub}' (referenced in {item.get_name()})")
                    img_tag.decompose()

            # --- Process Links within this item ---
            for a_tag in body_content.find_all('a', href=True): # Ensure href exists
                href = a_tag['href']

                # Skip external links and mailto links
                if href.startswith(('http://', 'https://', 'mailto:', 'tel:')):
                    a_tag['target'] = '_blank'
                    a_tag['rel'] = 'noopener noreferrer'
                    continue

                # Skip pure anchor links within the same original file for now
                if href.startswith('#'):
                    # Sanitize the anchor itself just in case
                    anchor_target = sanitize_id(href[1:])
                    if anchor_target:
                         a_tag['href'] = '#' + anchor_target
                    else:
                         # If anchor becomes invalid after sanitizing, remove link
                         a_tag.replace_with(a_tag.get_text() + " [invalid anchor link]")
                    continue

                # Process internal EPUB links
                try:
                    base_path = os.path.dirname(item.get_name())
                    target_full_path = os.path.normpath(os.path.join(base_path, href))
                    target_full_path = target_full_path.replace('\\', '/')

                    target_file_path = target_full_path.split('#')[0]
                    target_anchor = target_full_path.split('#')[1] if '#' in target_full_path else None

                    # Check if the target file path exists in our item_id_map
                    if target_file_path in item_id_map:
                        target_div_id = item_id_map[target_file_path]
                        new_href = f"#{target_div_id}"
                        if target_anchor:
                            sanitized_anchor = sanitize_id(target_anchor)
                            if sanitized_anchor:
                                # Append the anchor ID. Note: HTML5 allows multiple IDs in fragments,
                                # but browser support/behavior varies. Linking to just the chapter
                                # div might be more reliable unless specific anchors are crucial.
                                # For simplicity, we append, but this might need refinement.
                                # Example: href="#chapter-chap1_xhtml#specific_id"
                                new_href += f"#{sanitized_anchor}"

                        a_tag['href'] = new_href
                        # logging.info(f"    Rewrote internal link: '{href}' -> '{new_href}'") # Reduce log noise
                    else:
                        logging.warning(f"    Internal link target file not found or not processed: '{target_file_path}' (from link '{href}' in {item.get_name()})")
                        a_tag.replace_with(a_tag.get_text() + " [invalid link]")

                except Exception as e:
                    logging.warning(f"    Error processing link '{href}' in {item.get_name()}: {e}")
                    a_tag.replace_with(a_tag.get_text() + " [link error]")


            # Create a wrapper div for this chapter/section's content
            chapter_wrapper = soup.new_tag('div', id=chapter_id, **{'class': 'epub-chapter'})

            # Move all direct children of body_content into the wrapper
            # This preserves the structure within the body
            content_to_move = list(body_content.contents)
            for child in content_to_move:
                 if isinstance(child, (NavigableString, Tag)):
                     chapter_wrapper.append(child.extract())

            # Append the wrapper's *inner HTML* to the full content string
            # Using decode_contents() preserves the tags inside the wrapper
            html_content_full += chapter_wrapper.decode_contents()
            html_content_full += "\n\n" # Visual separation in source

        except Exception as e:
            logging.error(f"  Error processing item {item.get_name()}: {e}", exc_info=True)
            html_content_full += f'<div class="epub-chapter error" id="error-{sanitize_id(item.get_name())}"><p><i>Error processing content from {item.get_name()}. See logs for details.</i></p></div>\n\n'


    # --- Generate Table of Contents (TOC) ---
    toc_html = '<nav class="toc" aria-labelledby="toc-heading"><h2 id="toc-heading">Table of Contents</h2><ul>\n'
    toc_items_generated = 0
    if book.toc:
        logging.info("Generating Table of Contents...")
        try:
            # Recursive function to handle nested TOC
            def generate_toc_level(toc_list, level=0):
                nonlocal toc_items_generated
                html = ""
                indent = "  " * (level + 1)
                for item in toc_list:
                    current_item_html = ""
                    link_href = "#" # Default href
                    link_title = "Untitled Section"
                    is_link_valid = False

                    if isinstance(item, epub.Link): # Simple link item
                        link_href = item.href
                        link_title = item.title
                    elif isinstance(item, (tuple, list)) and len(item) > 0 and isinstance(item[0], epub.Link): # Nested section [Link, [sublist]]
                        link_href = item[0].href
                        link_title = item[0].title
                    else:
                        logging.warning(f"Unexpected TOC item structure: {type(item)}")
                        continue # Skip this item

                    # Process the link
                    target_file = link_href.split('#')[0]
                    target_anchor = link_href.split('#')[1] if '#' in link_href else None

                    if target_file in item_id_map:
                        target_div_id = item_id_map[target_file]
                        final_href = f"#{target_div_id}"
                        if target_anchor:
                            sanitized_anchor = sanitize_id(target_anchor)
                            if sanitized_anchor: final_href += f"#{sanitized_anchor}"
                        current_item_html += f'{indent}<li><a href="{final_href}">{link_title}</a>';
                        toc_items_generated += 1;
                        is_link_valid = True;
                    else:
                        current_item_html += f'{indent}<li>{link_title} (Link target not found)';
                        logging.warning(f"TOC link target file not found: {target_file} (from link '{link_href}')");

                    # Handle nesting
                    if isinstance(item, (tuple, list)) and len(item) > 1 and isinstance(item[1], (tuple, list)):
                        current_item_html += f"\n{indent}  <ul>\n";
                        current_item_html += generate_toc_level(item[1], level + 1);
                        current_item_html += f"{indent}  </ul>\n{indent}";

                    current_item_html += "</li>\n";
                    html += current_item_html;
                return html;

            toc_html += generate_toc_level(book.toc);

        except Exception as e:
            logging.error(f"Error processing Table of Contents: {e}", exc_info=True)
            toc_html += "<li>Error generating TOC. See logs.</li>\n"
    else:
        logging.warning("No Table of Contents (TOC) found in EPUB metadata.")
        toc_html += "<li>(No table of contents available)</li>\n"

    toc_html += '</ul></nav>\n<hr>\n'

    # Conditional TOC inclusion
    if toc_items_generated == 0 and book.toc:
         final_toc_html = '<nav class="toc" aria-labelledby="toc-heading"><h2 id="toc-heading">Table of Contents</h2><p>(Error generating links for TOC)</p></nav>\n<hr>\n'
    elif toc_items_generated > 0 or not book.toc:
         final_toc_html = toc_html
    else:
         final_toc_html = ""


    # --- Assemble Final HTML ---
    # Link to Google Fonts for Noto Sans SC (optional but recommended if users might not have it)
    google_font_link = '<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;600&display=swap" rel="stylesheet">'

    final_html = f"""<!DOCTYPE html>
<html lang="{book_language}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{book_title}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    {google_font_link}
    <style>
    {css_content}
    </style>
</head>
<body>
    <button id="theme-toggle" class="theme-toggle" aria-label="Toggle theme">🌙</button>

    <div class="content-wrapper">
        <h1>{book_title}</h1>

        {final_toc_html}

        <article class="main-content" role="main">
            {html_content_full}
        </article>
    </div>

    <!-- Font size controls -->
    <div class="font-controls">
        <button class="font-button" id="decrease-font" aria-label="Decrease font size">A-</button>
        <button class="font-button" id="increase-font" aria-label="Increase font size">A+</button>
    </div>

    <script>
        // --- Theme Toggle --- 
        const themeToggle = document.getElementById('theme-toggle');
        const body = document.body;
        const prefersDarkScheme = window.matchMedia("(prefers-color-scheme: dark)");

        // Function to apply theme based on preference
        function applyTheme(theme) {{
            if (theme === 'dark') {{
                body.classList.add('dark-mode');
                themeToggle.textContent = '☀️'; // Sun icon for switching to light
                themeToggle.setAttribute('aria-label', 'Switch to light mode');
            }} else {{
                body.classList.remove('dark-mode');
                themeToggle.textContent = '🌙'; // Moon icon for switching to dark
                themeToggle.setAttribute('aria-label', 'Switch to dark mode');
            }}
        }}

        // Load saved theme from localStorage or use system preference
        const savedTheme = localStorage.getItem('theme');
        let currentTheme = savedTheme;

        if (!currentTheme) {{
            currentTheme = prefersDarkScheme.matches ? 'dark' : 'light';
        }}

        applyTheme(currentTheme);

        // Listen for system theme changes
        prefersDarkScheme.addEventListener('change', (e) => {{
            // Only update if no theme is explicitly saved in localStorage
            if (!localStorage.getItem('theme')) {{
                applyTheme(e.matches ? 'dark' : 'light');
            }}
        }});

        // Handle button click
        themeToggle.addEventListener('click', () => {{
            const newTheme = body.classList.contains('dark-mode') ? 'light' : 'dark';
            applyTheme(newTheme);
            localStorage.setItem('theme', newTheme); // Save preference
        }});

        // --- Font Size Control ---
        // Ensure DOM is fully loaded before initializing
        document.addEventListener('DOMContentLoaded', function() {{
            initFontSizeControls();
        }});

        // Also initialize immediately in case the DOM is already loaded
        if (document.readyState === "complete" || document.readyState === "loaded" || document.readyState === "interactive") {{
            initFontSizeControls();
        }}

        function initFontSizeControls() {{
            // Get font size control buttons
            const increaseFontBtn = document.getElementById('increase-font');
            const decreaseFontBtn = document.getElementById('decrease-font');
            
            // Define font size limits and step size
            const minFontSize = 0.5;  // Minimum font size
            const maxFontSize = 1.5;  // Maximum font size
            const defaultFontSize = 0.8; // Default font size
            const step = 0.1;         // Step size for changes
            
            // Function to get computed font size as a number (in em)
            function getCurrentFontSize() {{
                const savedSize = parseFloat(localStorage.getItem('fontSizeEpub'));
                if (!isNaN(savedSize) && savedSize >= minFontSize && savedSize <= maxFontSize) {{
                    return savedSize; // Return validated saved size
                }}
                return defaultFontSize; // Return default if saved value is invalid
            }}
            
            // Function to apply font size to body with validation
            function applyFontSize(size) {{
                // Validate size is within acceptable range
                if (isNaN(size) || size < minFontSize) size = minFontSize;
                if (size > maxFontSize) size = maxFontSize;
                
                // Round to 1 decimal place to avoid floating point issues
                const roundedSize = Math.round(size * 10) / 10;
                
                // Apply size to body element with !important to override other styles
                document.body.style.setProperty('font-size', roundedSize + 'em', 'important');
                console.log('Font size set to:', roundedSize + 'em');
                
                // Save to localStorage for persistence
                localStorage.setItem('fontSizeEpub', roundedSize);
                
                // Update button states based on limits
                decreaseFontBtn.disabled = (roundedSize <= minFontSize);
                increaseFontBtn.disabled = (roundedSize >= maxFontSize);
            }}
            
            // Initialize with current/saved font size
            const initialSize = getCurrentFontSize();
            applyFontSize(initialSize);
            
            // Set up event listeners for buttons
            increaseFontBtn.addEventListener('click', function() {{
                const currentSize = getCurrentFontSize();
                applyFontSize(currentSize + step);
            }});
            
            decreaseFontBtn.addEventListener('click', function() {{
                const currentSize = getCurrentFontSize();
                applyFontSize(currentSize - step);
            }});
            
            // Additional keyboard shortcuts for accessibility
            document.addEventListener('keydown', function(e) {{
                // Alt + Plus for increase, Alt + Minus for decrease
                if (e.altKey) {{
                    if (e.key === '+' || e.key === '=') {{
                        e.preventDefault();
                        const currentSize = getCurrentFontSize();
                        applyFontSize(currentSize + step);
                    }} else if (e.key === '-' || e.key === '_') {{
                        e.preventDefault();
                        const currentSize = getCurrentFontSize();
                        applyFontSize(currentSize - step);
                    }}
                }}
            }});
            
            // Log that initialization is complete
            console.log('Font size controls initialized successfully');
        }}
    </script>
</body>
</html>
    """

    output_html_path = os.path.join(output_dir, "output.html")
    try:
        with codecs.open(output_html_path, "w", "utf-8") as f:
            f.write(final_html)
        logging.info("-" * 30)
        logging.info(f"Successfully converted EPUB to HTML!")
        logging.info(f"Output HTML: {output_html_path}")
        logging.info(f"Images saved in: {img_dir}")
        logging.info("-" * 30)
        return os.path.abspath(output_html_path) # Return the absolute path
    except IOError as e:
        logging.error(f"Error writing final HTML file {output_html_path}: {e}")
        return None
    except Exception as e:
        logging.error(f"Unexpected error writing final HTML file: {e}")
        return None


# --- Main Execution (for standalone use) ---
def run_standalone():
    # --- Crucial Check ---
    # Define default paths here for standalone execution
    default_epub_path = "./demo.epub" # Or prompt user
    default_output_folder = "output_html_book"

    epub_file_to_process = input(f"Enter EPUB file path [{default_epub_path}]: ") or default_epub_path
    output_folder_name = input(f"Enter output folder name [{default_output_folder}]: ") or default_output_folder


    if not os.path.exists(epub_file_to_process):
         logging.error("=" * 60)
         logging.error(" EPUB FILE NOT FOUND! ".center(60, '*'))
         logging.error(f" Please ensure '{epub_file_to_process}' exists or provide the correct path. ".center(60))
         logging.error("=" * 60)

    else:
        # Construct full output path relative to the script's directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        if os.path.isabs(output_folder_name):
             output_directory = output_folder_name
        else:
             output_directory = os.path.join(script_dir, output_folder_name)

        # Run the conversion
        result_path = extract_and_convert_epub(os.path.abspath(epub_file_to_process), output_directory)

        if result_path:
            logging.info(f"Conversion successful. Output HTML: {result_path}")
        else:
            logging.error("EPUB conversion failed.")


if __name__ == "__main__":
    run_standalone() # Call the standalone execution function
