import re
import os
import argparse
from pathlib import Path

def fix_timestamp_format(timestamp):
    """
    Fix timestamp format by ensuring it has hours segment.
    Convert "MM:SS,mmm" to "HH:MM:SS,mmm".
    """
    if timestamp.count(':') == 1:
        return f"00:{timestamp}"
    return timestamp

def parse_srt(srt_file):
    """Parse an SRT file into a list of subtitle entries."""
    with open(srt_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split the content by subtitle entries (handles both correct and incorrect timestamp formats)
    pattern = r'(\d+)\n(\d{2}:\d{2}(?::\d{2})?,\d{3}) --> (\d{2}:\d{2}(?::\d{2})?,\d{3})\n([\s\S]*?)(?=\n\d+\n|$)'
    entries = re.findall(pattern, content)
    
    parsed_entries = []
    for entry in entries:
        index = entry[0]
        start_time = fix_timestamp_format(entry[1])
        end_time = fix_timestamp_format(entry[2])
        timestamp = f"{start_time} --> {end_time}"
        text = entry[3].strip()
        parsed_entries.append({
            'index': index,
            'timestamp': timestamp,
            'text': text
        })
    
    return parsed_entries

def detect_languages(text):
    """
    Detect and properly separate languages based on Unicode character ranges.
    Returns a tuple of (english_text, chinese_text).
    """
    lines = text.split('\n')
    english_lines = []
    chinese_lines = []
    
    for line in lines:
        # Count Chinese characters in the line
        chinese_char_count = sum(1 for c in line if '\u4e00' <= c <= '\u9fff')
        
        # If the line has Chinese characters, add it to Chinese lines
        if chinese_char_count > 0:
            chinese_lines.append(line)
        # Otherwise, add it to English lines
        else:
            english_lines.append(line)
    
    # Join the lines with appropriate spacing
    english_text = ' '.join([line.strip() for line in english_lines]) if english_lines else ''
    chinese_text = ''.join([line.strip() for line in chinese_lines]) if chinese_lines else ''
    
    return english_text, chinese_text

def extract_multilingual(input_file, output1=None, output2=None):
    """
    Fix timestamp formats and extract multilingual subtitles into separate files.
    Each caption will be on a single line per language.
    """
    # Define output filenames if not provided
    input_path = Path(input_file)
    base_name = input_path.stem
    
    if output1 is None:
        output1 = str(input_path.with_name(f"{base_name}_eng.srt"))
    if output2 is None:
        output2 = str(input_path.with_name(f"{base_name}_chn.srt"))
    
    # Parse the SRT file and fix timestamps
    parsed_entries = parse_srt(input_file)
    
    # Prepare entries for each language
    english_entries = []
    chinese_entries = []
    
    for entry in parsed_entries:
        index = entry['index']
        timestamp = entry['timestamp']
        text = entry['text']
        
        # Detect and separate languages
        english_text, chinese_text = detect_languages(text)
        
        # Add entries for each language if they have content
        if english_text:
            english_entries.append({
                'index': index,
                'timestamp': timestamp,
                'text': english_text
            })
        
        if chinese_text:
            chinese_entries.append({
                'index': index,
                'timestamp': timestamp,
                'text': chinese_text
            })
    
    # Write the output files
    def write_srt(entries, output_file):
        with open(output_file, 'w', encoding='utf-8') as f:
            for i, entry in enumerate(entries, 1):
                f.write(f"{i}\n")  # Renumber entries sequentially
                f.write(f"{entry['timestamp']}\n")
                f.write(f"{entry['text']}\n\n")
    
    write_srt(english_entries, output1)
    write_srt(chinese_entries, output2)
    
    print(f"English subtitles written to: {output1}")
    print(f"Chinese subtitles written to: {output2}")
    
    return output1, output2

def main():
    parser = argparse.ArgumentParser(description='Fix SRT timestamp format and extract multilingual subtitles.')
    parser.add_argument('input_file', help='Path to the input SRT file')
    parser.add_argument('--output1', help='Output file for English (optional)')
    parser.add_argument('--output2', help='Output file for Chinese (optional)')

    args = parser.parse_args()

    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' not found.")
        return

    extract_multilingual(args.input_file, args.output1, args.output2)

if __name__ == '__main__':
    main()