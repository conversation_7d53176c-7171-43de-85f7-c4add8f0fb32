import re
import argparse
import os
from pathlib import Path

def fix_timestamp_format(timestamp):
    """
    Fix timestamp format by ensuring it has hours segment.
    Convert "MM:SS,mmm" to "HH:MM:SS,mmm".
    """
    if timestamp.count(':') == 1:
        return f"00:{timestamp}"
    return timestamp

def parse_srt(content):
    """Parse SRT content into a list of subtitle entries."""
    # Split the content by subtitle entries
    pattern = r'(\d+)\n(\d{2}:\d{2}(?::\d{2})?,\d{3} --> \d{2}:\d{2}(?::\d{2})?,\d{3})\n([\s\S]*?)(?=\n\d+\n|$)'
    entries = re.findall(pattern, content)
    
    parsed_entries = []
    for entry in entries:
        index = entry[0]
        timestamp = entry[1]
        text = entry[2].strip()
        parsed_entries.append({
            'index': index,
            'timestamp': timestamp,
            'text': text
        })
    
    return parsed_entries

def merge_text_to_single_line(text):
    """
    Separate multilingual text and merge each language into a single line.
    Detects languages primarily based on character encoding.
    Returns a tuple of (english_text, non_english_text).
    """
    lines = text.split('\n')
    
    english_lines = []
    non_english_lines = []
    
    for line in lines:
        line = line.strip()
        if not line:  # Skip empty lines
            continue
            
        # Check if the line contains non-ASCII characters (likely non-English)
        if any(ord(c) > 127 for c in line):
            non_english_lines.append(line)
        else:
            english_lines.append(line)
    
    # Merge lines for each language
    english_text = ' '.join(english_lines) if english_lines else ''
    
    # For non-English (like Chinese), join without spaces
    non_english_text = ''.join(non_english_lines) if non_english_lines else ''
    
    return english_text, non_english_text

def extract_multilingual(input_file, output1=None, output2=None):
    """
    Fix timestamp formats and extract multilingual subtitles into separate files.
    Each caption will be on a single line per language.
    """
    # Define regex pattern for timestamps (both correct and incorrect formats)
    timestamp_pattern = re.compile(r'(\d{2}:\d{2}(?::\d{2})?,\d{3}) --> (\d{2}:\d{2}(?::\d{2})?,\d{3})')

    # Generate default output filenames if not provided
    input_path = Path(input_file)
    base_name = input_path.stem
    
    if output1 is None:
        output1 = str(input_path.with_name(f"{base_name}_lang1.srt"))
    if output2 is None:
        output2 = str(input_path.with_name(f"{base_name}_lang2.srt"))

    # Read the input file
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # Fix all timestamp lines
    def replace_timestamps(match):
        start_time = fix_timestamp_format(match.group(1))
        end_time = fix_timestamp_format(match.group(2))
        return f"{start_time} --> {end_time}"

    fixed_content = timestamp_pattern.sub(replace_timestamps, content)
    
    # Parse the fixed content
    parsed_entries = parse_srt(fixed_content)
    
    # Prepare entries for each language
    first_lang_entries = []
    second_lang_entries = []
    
    for entry in parsed_entries:
        index = entry['index']
        timestamp = entry['timestamp']
        text = entry['text']
        
        # Split and merge the text for each language
        first_lang_text, second_lang_text = merge_text_to_single_line(text)
        
        # Only add entries if they have content
        if first_lang_text:
            first_lang_entries.append({
                'index': index,
                'timestamp': timestamp,
                'text': first_lang_text
            })
        
        if second_lang_text:
            second_lang_entries.append({
                'index': index,
                'timestamp': timestamp,
                'text': second_lang_text
            })
    
    # Write the output files
    def write_srt(entries, output_file):
        with open(output_file, 'w', encoding='utf-8') as f:
            for i, entry in enumerate(entries, 1):
                f.write(f"{i}\n")  # Renumber entries sequentially
                f.write(f"{entry['timestamp']}\n")
                f.write(f"{entry['text']}\n\n")
    
    write_srt(first_lang_entries, output1)
    write_srt(second_lang_entries, output2)
    
    print(f"First language written to: {output1}")
    print(f"Second language written to: {output2}")
    
    return output1, output2

def main():
    parser = argparse.ArgumentParser(description='Fix SRT timestamp format and extract multilingual subtitles.')
    parser.add_argument('input_file', help='Path to the input SRT file')
    parser.add_argument('--output1', help='Output file for first language (optional)')
    parser.add_argument('--output2', help='Output file for second language (optional)')

    args = parser.parse_args()

    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' not found.")
        return

    extract_multilingual(args.input_file, args.output1, args.output2)

if __name__ == '__main__':
    main()