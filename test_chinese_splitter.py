import re

def split_chinese_sentences_quote_aware_regex(paragraph):
    """
    Splits a Chinese paragraph into sentences, aware of punctuation within quotes.
    Handles '。”', '！”', '？”', '……”', and also '”[。？！]'.
    """
    # 1. Normalize ellipsis to a single token temporarily for consistent handling.
    #    We replace '……' (two full-width dots) with a placeholder.
    paragraph = paragraph.replace('……', '<ELLIPSIS_TOKEN>')

    # 2. Define the sentence-ending patterns.
    #    Order matters: longest/most specific patterns first.
    #    - `。”|！”|？”`: Punctuation immediately followed by a closing quote.
    #    - `”[。？！]`: Closing quote immediately followed by punctuation (for main sentence).
    #    - `[。？！]`: Standalone punctuation.
    #    - `\n`: Newline.
    
    # The parentheses `(...)` create a capturing group, so re.split includes the delimiter.
    sentence_endings_pattern = r'(。”|！”|？”|”[。？！]|[。？！]|\n)'
    
    # Split the paragraph using the defined patterns
    parts = re.split(sentence_endings_pattern, paragraph)
    
    sentences = []
    current_sentence_buffer = [] # Buffer to collect parts of the current sentence
    
    # Iterate through the parts generated by re.split
    for part in parts:
        if part is None: # re.split can produce None for unmatched optional groups
            continue
        
        part_stripped = part.strip()
        
        if not part_stripped: # Skip empty strings (e.g., from multiple delimiters, leading/trailing whitespace)
            continue
        
        # Check if this part is a recognized delimiter using re.fullmatch
        # re.fullmatch ensures the *entire* part matches one of our delimiter patterns
        is_delimiter = re.fullmatch(sentence_endings_pattern, part)
        
        if is_delimiter:
            # If the buffer is not empty, it means we've just completed a sentence.
            if current_sentence_buffer:
                # Append the delimiter to the sentence buffer
                current_sentence_buffer.append(part)
                
                # Join the parts, replace ellipsis token, strip whitespace, and add to sentences list
                full_sentence = "".join(current_sentence_buffer).replace('<ELLIPSIS_TOKEN>', '……').strip()
                if full_sentence: # Ensure we don't add empty strings
                    sentences.append(full_sentence)
                current_sentence_buffer = [] # Reset buffer for the next sentence
            else:
                # This case means a delimiter appeared without preceding content (e.g., at the start of paragraph, or double delimiter)
                # We generally ignore these as they don't form a complete sentence unit.
                pass
        else:
            # This part is content (not a delimiter). Add it to the current sentence buffer.
            current_sentence_buffer.append(part)
    
    # After the loop, if there's any remaining content in the buffer, it's the last sentence.
    if current_sentence_buffer:
        final_sentence = "".join(current_sentence_buffer).replace('<ELLIPSIS_TOKEN>', '……').strip()
        if final_sentence:
            sentences.append(final_sentence)
            
    return sentences

# --- Test Cases for Quote Situations ---
text1 = "他说：“我很好。”然后他微笑着离开了。"
text2 = "她大喊：“救命啊！”旁边的人都惊呆了。"
text3 = "有人问：“你在干什么？”我回答说：“我正在看书。”"
text4 = "这个例子……“请看！”很有趣。"
text5 = "“你好！”他说道，然后继续前行。" # Quote followed by punctuation
text6 = "他说：“是的。”“不，不是的。”她反驳道。" # Consecutive quoted sentences
text7 = "这是个简单的句子。这也是个简单的句子。”他总结道。最后一句。" # Mixed
text8 = "她低声说：“我爱你……”他听到了吗？" # Ellipsis inside quote
text9 = "他说：“我真的没事。”\n她没有说什么。" # Newline after quoted sentence

print("--- Example 1 (。”) ---")
for i, sentence in enumerate(split_chinese_sentences_quote_aware_regex(text1)):
    print(f"{i+1}: {sentence}")

print("\n--- Example 2 (！” ) ---")
for i, sentence in enumerate(split_chinese_sentences_quote_aware_regex(text2)):
    print(f"{i+1}: {sentence}")

print("\n--- Example 3 (？” and consecutive quotes) ---")
for i, sentence in enumerate(split_chinese_sentences_quote_aware_regex(text3)):
    print(f"{i+1}: {sentence}")

print("\n--- Example 4 (…… and then “！” ) ---")
for i, sentence in enumerate(split_chinese_sentences_quote_aware_regex(text4)):
    print(f"{i+1}: {sentence}")

print("\n--- Example 5 (”！) ---")
for i, sentence in enumerate(split_chinese_sentences_quote_aware_regex(text5)):
    print(f"{i+1}: {sentence}")

print("\n--- Example 6 (Consecutive quoted sentences) ---")
for i, sentence in enumerate(split_chinese_sentences_quote_aware_regex(text6)):
    print(f"{i+1}: {sentence}")

print("\n--- Example 7 (Mixed with trailing quote) ---")
for i, sentence in enumerate(split_chinese_sentences_quote_aware_regex(text7)):
    print(f"{i+1}: {sentence}")

print("\n--- Example 8 (Ellipsis inside quote) ---")
for i, sentence in enumerate(split_chinese_sentences_quote_aware_regex(text8)):
    print(f"{i+1}: {sentence}")

print("\n--- Example 9 (Newline after quoted sentence) ---")
for i, sentence in enumerate(split_chinese_sentences_quote_aware_regex(text9)):
    print(f"{i+1}: {sentence}")