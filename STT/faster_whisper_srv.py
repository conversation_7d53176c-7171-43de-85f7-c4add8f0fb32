import argparse
from faster_whisper import Whis<PERSON>Mode<PERSON>, BatchedInferencePipeline

def main(audio_file):
    model_size = "large-v3-turbo"
    model = WhisperModel(model_size, device="cpu", compute_type="float32")
    batched_model = BatchedInferencePipeline(model=model)
    segments, info = batched_model.transcribe(
        audio_file, 
        batch_size=16,
        chunk_length=10.0,  # Reduce from default 30s to 10s for better caption length
        max_new_tokens=64,    # Limit tokens to keep captions concise
        no_repeat_ngram_size=3 # Prevent repetition of 3-grams
    )

    segments_list = list(segments)  # Convert generator to list to allow multiple iterations

    # Determine output file names
    # Assuming audio_file path might be like "path/to/audio.wav"
    # We want "path/to/audio.srt" and "path/to/audio.txt"
    if '.' in audio_file:
        base_name = audio_file.rsplit('.', 1)[0]
    else:
        base_name = audio_file
        
    srt_file_path = f"{base_name}.srt"
    txt_file_path = f"{base_name}.txt"

    # Helper function to format time for SRT
    def format_time_srt(seconds):
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millis = int((seconds - int(seconds)) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millis:03d}"

    # Write SRT file
    with open(srt_file_path, "w", encoding="utf-8") as srt_file:
        for i, segment in enumerate(segments_list):
            start_time = format_time_srt(segment.start)
            end_time = format_time_srt(segment.end)
            srt_file.write(f"{i + 1}\n")
            srt_file.write(f"{start_time} --> {end_time}\n")
            srt_file.write(f"{segment.text.strip()}\n\n")
    print(f"Transcription saved to {srt_file_path}")

    # Write text file (without timestamps)
    with open(txt_file_path, "w", encoding="utf-8") as txt_file:
        full_text = " ".join(segment.text.strip() for segment in segments_list)
        txt_file.write(full_text)
    print(f"Transcription text saved to {txt_file_path}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Transcribe an audio file using Faster Whisper.")
    parser.add_argument("audio_file", type=str, help="Path to the audio file to transcribe.")
    args = parser.parse_args()
    main(args.audio_file)
