def parse_time(time_str):
    """Convert SRT timestamp to milliseconds"""
    hours, minutes, seconds = time_str.replace(',', ':').split(':')
    return int(hours) * 3600000 + int(minutes) * 60000 + int(seconds) * 1000 + int(seconds)

def format_time(ms):
    """Convert milliseconds to SRT timestamp format"""
    hours = ms // 3600000
    ms %= 3600000
    minutes = ms // 60000
    ms %= 60000
    seconds = ms // 1000
    ms %= 1000
    return f"{hours:02d}:{minutes:02d}:{seconds:02d},{ms:03d}"

def merge_srt_lines(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    merged_subs = []
    current_sub = {}
    line_count = 0
    
    for line in lines:
        line = line.strip()
        if not line:
            if current_sub:
                merged_subs.append(current_sub)
                current_sub = {}
            continue
            
        if line.isdigit():
            current_sub['index'] = line
        elif ' --> ' in line:
            current_sub['time'] = line
        else:
            current_sub['text'] = line.strip()

    # Merge pairs of subtitles
    merged_result = []
    for i in range(0, len(merged_subs), 2):
        if i + 1 < len(merged_subs):
            # Get time ranges
            start_time = merged_subs[i]['time'].split(' --> ')[0]
            end_time = merged_subs[i+1]['time'].split(' --> ')[1]
            
            # Combine text
            combined_text = f"{merged_subs[i]['text']}\n{merged_subs[i+1]['text']}"
            
            # Create merged entry
            merged_entry = {
                'index': str(len(merged_result) + 1),
                'time': f"{start_time} --> {end_time}",
                'text': combined_text
            }
            merged_result.append(merged_entry)
        else:
            # Handle odd number of subtitles
            merged_result.append(merged_subs[i])

    # Write output
    with open(output_file, 'w', encoding='utf-8') as f:
        for entry in merged_result:
            f.write(f"{entry['index']}\n")
            f.write(f"{entry['time']}\n")
            f.write(f"{entry['text']}\n\n")

if __name__ == "__main__":
    import sys
    if len(sys.argv) != 3:
        print("Usage: python merge_srt_lines.py input.srt output.srt")
        sys.exit(1)
        
    merge_srt_lines(sys.argv[1], sys.argv[2])
